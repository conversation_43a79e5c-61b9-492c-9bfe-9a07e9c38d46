import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { of, throwError, Subject } from 'rxjs';
import { GridApi } from 'ag-grid-community';

import { WorkflowsComponent } from './workflows.component';
import { WorkflowService } from '../../../services/workflow/workflow.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { ToastService } from '../../../services/toast.service';
import { FormAction } from '../../../models/form-action';
import { ActionState } from '../../../components/datagrid/models/action-state';
import { DatagridActionsCta } from '../../../components/datagrid/models/enums/datagrid-actions-cta';
import { IManageWorkflow } from '../../../models/workflow/manage-workflow.model';
import { DataGridMessage } from '../../../components/datagrid/models/datagrid-message.model';

// Mock grid utils
jest.mock('../../../utils/grid-utils', () => ({
  getGridData: jest.fn(),
  handleCancelAction: jest.fn(() => of(null))
}));

// Import the mocked functions
import { getGridData } from '../../../utils/grid-utils';
const mockGetGridData = getGridData as jest.MockedFunction<typeof getGridData>;

describe('WorkflowsComponent', () => {
  let component: WorkflowsComponent;
  let fixture: ComponentFixture<WorkflowsComponent>;
  let mockWorkflowService: jest.Mocked<WorkflowService>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockRouter: jest.Mocked<Router>;
  let editingStateSubject: Subject<FormAction>;

  const mockWorkflows: IManageWorkflow[] = [
    {
      id: '1',
      workflow: 'Test Workflow 1',
      description: 'Test Description 1',
      version: 1,
      entity: 'Entity1',
      completed: false,
      canDelete: true
    },
    {
      id: '2',
      workflow: 'Test Workflow 2',
      description: 'Test Description 2',
      version: 2,
      entity: 'Entity2',
      completed: true,
      canDelete: false
    }
  ];

  const mockEntities = ['Entity1', 'Entity2', 'Entity3'];

  beforeEach(async () => {
    editingStateSubject = new Subject<FormAction>();

    mockWorkflowService = {
      findManagedWorkflows: jest.fn(),
      findWorkflowEntities: jest.fn(),
      updateWorkflows: jest.fn(),
      findWorkflows: jest.fn(),
      createWorkflow: jest.fn(),
      deleteWorkflow: jest.fn()
    } as any;

    mockEditingStateService = {
      getValue: jest.fn(() => editingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: { data: [], isEditing: false, url: {} }
    } as any;

    mockDialogMessageService = {
      displayError: jest.fn(),
      displaySuccess: jest.fn(),
      displayWarning: jest.fn()
    } as any;

    mockToastService = {
      displayError: jest.fn(),
      displaySuccess: jest.fn(),
      displayWarning: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [WorkflowsComponent],
      providers: [
        { provide: WorkflowService, useValue: mockWorkflowService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: ToastService, useValue: mockToastService },
        { provide: Router, useValue: mockRouter }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    // Setup default successful responses
    mockWorkflowService.findManagedWorkflows.mockReturnValue(of(mockWorkflows));
    mockWorkflowService.findWorkflowEntities.mockReturnValue(of(mockEntities));
    mockWorkflowService.updateWorkflows.mockReturnValue(of(mockWorkflows));

    fixture = TestBed.createComponent(WorkflowsComponent);
    component = fixture.componentInstance;

    // Avoid template rendering issues by not calling detectChanges in beforeEach
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should be defined', () => {
      expect(component).toBeDefined();
    });

    it('should be an instance of WorkflowsComponent', () => {
      expect(component instanceof WorkflowsComponent).toBe(true);
    });

    it('should initialize with default values', () => {
      expect(component.rowData).toEqual([]);
      expect(component.errors).toEqual([]);
      expect(component.path).toBe(location.pathname);
      expect(component.workflowEntities).toBeUndefined();
      expect(component.columnDefs).toBeUndefined();
    });
  });

  describe('Constructor and Subscriptions', () => {
    it('should subscribe to editing state service', () => {
      // Component constructor already calls getValue, no need for detectChanges
      expect(mockEditingStateService.getValue).toHaveBeenCalled();
    });

    it('should handle SAVE action from editing state', () => {
      const saveDataSpy = jest.spyOn(component as any, 'saveData').mockImplementation(() => {});

      // Trigger the subscription without detectChanges
      editingStateSubject.next(FormAction.SAVE);

      expect(saveDataSpy).toHaveBeenCalled();
    });

    it('should handle EDIT action from editing state', () => {
      component.rowData = mockWorkflows as any;

      // Trigger the subscription without detectChanges
      editingStateSubject.next(FormAction.EDIT);

      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['rowData', JSON.parse(JSON.stringify(component.rowData))]
      ]);
    });

    it('should not handle other actions from editing state', () => {
      const saveDataSpy = jest.spyOn(component as any, 'saveData').mockImplementation(() => {});

      // Trigger the subscription without detectChanges
      editingStateSubject.next(FormAction.CANCEL);

      expect(saveDataSpy).not.toHaveBeenCalled();
      expect(mockEditingStateService.setData).not.toHaveBeenCalled();
    });
  });

  describe('ngOnInit and Data Loading', () => {
    it('should call loadData on ngOnInit', () => {
      const loadDataSpy = jest.spyOn(component as any, 'loadData').mockImplementation(() => {});

      component.ngOnInit();

      expect(loadDataSpy).toHaveBeenCalled();
    });

    it('should load workflows and entities successfully', () => {
      // Call ngOnInit directly instead of detectChanges
      component.ngOnInit();

      expect(mockWorkflowService.findManagedWorkflows).toHaveBeenCalled();
      expect(mockWorkflowService.findWorkflowEntities).toHaveBeenCalled();
      expect(component.workflowEntities).toEqual(mockEntities);
      expect(component.rowData).toEqual(mockWorkflows);
    });

    it('should set action states based on canDelete property', () => {
      // Call ngOnInit directly instead of detectChanges
      component.ngOnInit();

      expect(component.rowData[0].action).toBe(ActionState.VIEW_ADD_EDIT_DELETE);
      expect(component.rowData[1].action).toBe(ActionState.VIEW_ADD_EDIT);
    });

    it('should call setColumnDefinition after loading data', () => {
      const setColumnDefinitionSpy = jest.spyOn(component, 'setColumnDefinition').mockImplementation(() => {});

      // Call ngOnInit directly instead of detectChanges
      component.ngOnInit();

      expect(setColumnDefinitionSpy).toHaveBeenCalled();
    });

    it('should handle error when loading workflows', () => {
      mockWorkflowService.findManagedWorkflows.mockReturnValue(throwError('Workflow error'));

      // Call ngOnInit directly instead of detectChanges
      component.ngOnInit();

      expect(mockToastService.displayError).toHaveBeenCalledWith(
        'Error occurred while loading grid data.'
      );
    });

    it('should handle error when loading entities', () => {
      mockWorkflowService.findWorkflowEntities.mockReturnValue(throwError('Entity error'));

      // Call ngOnInit directly instead of detectChanges
      component.ngOnInit();

      expect(mockToastService.displayError).toHaveBeenCalledWith(
        'Error occurred while loading Workflow Entities.'
      );
    });
  });

  describe('Column Definition', () => {
    beforeEach(() => {
      component.workflowEntities = mockEntities;
      component.setColumnDefinition();
    });

    it('should set column definitions correctly', () => {
      expect(component.columnDefs).toBeDefined();
      expect(component.columnDefs.length).toBe(5);
    });

    it('should have workflow column with correct properties', () => {
      const workflowColumn = component.columnDefs.find(col => col.field === 'workflow');

      expect(workflowColumn).toBeDefined();
      expect(workflowColumn.headerName).toBe('Workflow Process');
      expect(workflowColumn.editable).toBe(true);
      expect(workflowColumn.minWidth).toBe(200);
      expect(workflowColumn.cellEditor).toBe('agTextCellEditor');
      expect(workflowColumn.cellEditorParams.maxLength).toBe(100);
    });

    it('should have description column with correct properties', () => {
      const descriptionColumn = component.columnDefs.find(col => col.field === 'description');

      expect(descriptionColumn).toBeDefined();
      expect(descriptionColumn.editable).toBe(true);
      expect(descriptionColumn.minWidth).toBe(400);
      expect(descriptionColumn.cellEditor).toBe('agTextCellEditor');
      expect(descriptionColumn.cellEditorParams.maxLength).toBe(2047);
    });

    it('should have entity column with dropdown editor', () => {
      const entityColumn = component.columnDefs.find(col => col.field === 'entity');

      expect(entityColumn).toBeDefined();
      expect(entityColumn.editable).toBe(true);
      expect(entityColumn.minWidth).toBe(166);
      expect(entityColumn.maxWidth).toBe(166);
      expect(entityColumn.cellEditorParams().options).toEqual(mockEntities);
    });

    it('should have version column with correct properties', () => {
      const versionColumn = component.columnDefs.find(col => col.field === 'version');

      expect(versionColumn).toBeDefined();
      expect(versionColumn.minWidth).toBe(120);
      expect(versionColumn.maxWidth).toBe(120);
    });

    it('should have completed column with checkbox renderer', () => {
      const completedColumn = component.columnDefs.find(col => col.field === 'completed');

      expect(completedColumn).toBeDefined();
      expect(completedColumn.editable).toBe(false);
      expect(completedColumn.minWidth).toBe(120);
      expect(completedColumn.maxWidth).toBe(120);
      expect(completedColumn.cellRendererParams().disabled).toBe(true);
    });
  });

  describe('Grid Operations', () => {
    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'gridApi' } as any as GridApi;

      component.onGridIsReady(mockGridApi);

      expect(component.gridApi).toBe(mockGridApi);
    });

    it('should create new row with correct data', () => {
      component.workflowEntities = mockEntities;
      component.rowData = mockWorkflows as any;

      const newRow = component.onNewRow();

      expect(newRow).toEqual({
        level: 2,
        hierarchyName: '',
        description: '',
        version: 1,
        entity: mockEntities[0]
      });
    });

    it('should create new row when no existing data', () => {
      component.workflowEntities = mockEntities;
      component.rowData = [];

      const newRow = component.onNewRow();

      expect(newRow).toEqual({
        level: 0,
        hierarchyName: '',
        description: '',
        version: 1,
        entity: mockEntities[0]
      });
    });
  });

  describe('Data Validation', () => {
    beforeEach(() => {
      component.gridApi = {
        forEachNode: jest.fn()
      } as any as GridApi;
    });

    it('should validate data successfully when all workflows have names', () => {
      mockGetGridData.mockReturnValue([
        { workflow: 'Test Workflow 1', rowId: 0 },
        { workflow: 'Test Workflow 2', rowId: 1 }
      ]);

      const result = (component as any).validateData();

      expect(result).toBe(true);
      expect(component.errors).toEqual([]);
    });

    it('should fail validation when workflow name is missing', () => {
      mockGetGridData.mockReturnValue([
        { workflow: 'Test Workflow 1', rowId: 0 },
        { workflow: '', rowId: 1 },
        { workflow: null, rowId: 2 }
      ]);

      const result = (component as any).validateData();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        '1. Workflow Grid: Row 2: Workflow Process is required.\n2. Workflow Grid: Row 3: Workflow Process is required.',
        false
      );
      expect(component.errors).toEqual([]);
    });

    it('should reset errors after validation failure', () => {
      mockGetGridData.mockReturnValue([
        { workflow: '', rowId: 0 }
      ]);

      component.errors = ['Previous error'];
      const result = (component as any).validateData();

      expect(result).toBe(false);
      expect(component.errors).toEqual([]);
    });
  });

  describe('Data Saving', () => {
    beforeEach(() => {
      component.gridApi = {
        forEachNode: jest.fn()
      } as any as GridApi;
    });

    it('should save data successfully when validation passes', () => {
      const mockGridData = [
        { workflow: 'Test Workflow 1', rowId: 0, action: ActionState.VIEW_ADD_EDIT }
      ];
      mockGetGridData.mockReturnValue(mockGridData);

      jest.spyOn(component as any, 'validateData').mockReturnValue(true);

      (component as any).saveData();

      expect(mockWorkflowService.updateWorkflows).toHaveBeenCalledWith(mockGridData);
    });

    it('should handle successful save response', () => {
      const mockGridData = [
        { workflow: 'Test Workflow 1', rowId: 0, action: ActionState.VIEW_ADD_EDIT }
      ];
      mockGetGridData.mockReturnValue(mockGridData);

      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      mockWorkflowService.updateWorkflows.mockReturnValue(of(mockWorkflows));

      (component as any).saveData();

      expect(component.rowData).toEqual(mockWorkflows);
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Workflows saved successfully.');
    });

    it('should handle save error', () => {
      mockGetGridData.mockReturnValue([{ workflow: 'Test', rowId: 0 }]);

      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      mockWorkflowService.updateWorkflows.mockReturnValue(throwError('Save error'));

      (component as any).saveData();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Save workflows failed.');
    });

    it('should not save when validation fails', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(false);

      (component as any).saveData();

      expect(mockWorkflowService.updateWorkflows).not.toHaveBeenCalled();
    });
  });

  describe('Cell Actions', () => {
    it('should handle VIEW action', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: { workflow: 'test-workflow' },
        rowIndex: 0
      } as any;

      const onViewSpy = jest.spyOn(component as any, 'onView').mockImplementation(() => {});

      component.onCellAction(mockMessage);

      expect(onViewSpy).toHaveBeenCalledWith(mockMessage);
    });

    it('should navigate to workflow detail on view', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: { workflow: 'test-workflow' },
        rowIndex: 0
      } as any;

      // Mock window.location.pathname
      Object.defineProperty(window, 'location', {
        value: { pathname: '/admin/workflow/manage-workflow' },
        writable: true
      });

      (component as any).onView(mockMessage);

      expect(mockRouter.navigate).toHaveBeenCalledWith([
        '/admin/workflow/manage-workflow',
        'test-workflow'
      ]);
    });

    it('should not handle unknown actions', () => {
      const mockMessage: DataGridMessage = {
        action: 'UNKNOWN_ACTION' as any,
        rawData: { workflow: 'test-workflow' },
        rowIndex: 0
      } as any;

      const onViewSpy = jest.spyOn(component as any, 'onView').mockImplementation(() => {});

      component.onCellAction(mockMessage);

      expect(onViewSpy).not.toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy subject on ngOnDestroy', () => {
      const destroySubject = (component as any).destroy$;
      const nextSpy = jest.spyOn(destroySubject, 'next');
      const completeSpy = jest.spyOn(destroySubject, 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      // No need for detectChanges, just test the destroy logic
      const destroySubject = (component as any).destroy$;
      const nextSpy = jest.spyOn(destroySubject, 'next');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
    });
  });

  describe('Component Properties', () => {
    it('should have correct initial path', () => {
      expect(component.path).toBe(location.pathname);
    });

    it('should have RoleActions assigned', () => {
      expect(component.actions).toBeDefined();
    });

    it('should initialize with empty arrays', () => {
      expect(component.rowData).toEqual([]);
      expect(component.errors).toEqual([]);
    });

    it('should have public editingStateService', () => {
      expect(component.editingStateService).toBe(mockEditingStateService);
    });
  });

  describe('Error Handling', () => {
    it('should handle multiple validation errors', () => {
      mockGetGridData.mockReturnValue([
        { workflow: '', rowId: 0 },
        { workflow: null, rowId: 1 },
        { workflow: undefined, rowId: 2 }
      ]);

      component.gridApi = { forEachNode: jest.fn() } as any;

      const result = (component as any).validateData();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('Workflow Process is required'),
        false
      );
    });

    it('should handle empty grid data', () => {
      mockGetGridData.mockReturnValue([]);

      component.gridApi = { forEachNode: jest.fn() } as any;

      const result = (component as any).validateData();

      expect(result).toBe(true);
    });

    it('should handle null gridApi gracefully', () => {
      component.gridApi = null;

      expect(() => {
        (component as any).validateData();
      }).not.toThrow();
    });
  });

  describe('Template Integration', () => {
    it('should have component template defined', () => {
      // Test that the component has a template without rendering it
      expect(fixture.nativeElement).toBeTruthy();
    });

    it('should have columnDefs property for template binding', () => {
      component.columnDefs = [];
      expect(component.columnDefs).toBeDefined();
      expect(Array.isArray(component.columnDefs)).toBe(true);
    });

    it('should have rowData property for template binding', () => {
      component.rowData = mockWorkflows as any;
      expect(component.rowData).toBeDefined();
      expect(Array.isArray(component.rowData)).toBe(true);
    });

    it('should have path property for template binding', () => {
      expect(component.path).toBeDefined();
      expect(typeof component.path).toBe('string');
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined workflowEntities in onNewRow', () => {
      component.workflowEntities = undefined;
      component.rowData = [];

      expect(() => {
        component.onNewRow();
      }).toThrow();
    });

    it('should handle empty workflowEntities array', () => {
      component.workflowEntities = [];
      component.rowData = [];

      const newRow = component.onNewRow();

      expect(newRow.entity).toBeUndefined();
    });

    it('should handle missing action property in save response', () => {
      mockGetGridData.mockReturnValue([{ workflow: 'Test', rowId: 0 }]);

      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      mockWorkflowService.updateWorkflows.mockReturnValue(of(mockWorkflows));

      expect(() => {
        (component as any).saveData();
      }).not.toThrow();
    });

    it('should handle subscription cleanup properly', () => {
      // Test cleanup without detectChanges
      expect(() => {
        component.ngOnDestroy();
        fixture.destroy();
      }).not.toThrow();
    });
  });
});
