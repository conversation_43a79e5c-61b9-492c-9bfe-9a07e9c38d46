import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of, throwError, Subject } from 'rxjs';

import { TaskDefinitionComponent } from './task-definition.component';
import { EditingStateService } from '../../../../../services/editing-state.service';
import { RoleService } from '../../../../../services/admin/role.service';
import { WorkflowService } from '../../../../../services/workflow/workflow.service';
import { ToastService } from '../../../../../services/toast.service';
import { DialogMessageService } from '../../../../../services/dialog-message.service';
import { LovService } from '../../../../../services/lov/lov.service';
import { AccountService } from '../../../../../auth/account.service';
import { RoleActions } from '../../../../../models/enums/role-actions';
import { FormAction } from '../../../../../models/form-action';
import { TaskType } from '../../../../../models/enums/workflow/task-type.enum';

describe('TaskDefinitionComponent', () => {
  let component: TaskDefinitionComponent;
  let fixture: ComponentFixture<TaskDefinitionComponent>;
  let mockActivatedRoute: any;
  let mockRouter: any;
  let mockEditingStateService: any;
  let mockRoleService: any;
  let mockWorkflowService: any;
  let mockToastService: any;
  let mockDialogMessageService: any;
  let mockLovService: any;
  let mockAccountService: any;
  let mockConfig: any;

  const mockRole = {
    roleId: 'ROLE1',
    name: 'Test Role'
  };

  const mockWorkflow = {
    workflowType: {
      id: 'wf1',
      name: 'Test Workflow'
    }
  };

  const mockWorkflowTaskType = {
    taskId: 'task1',
    description: 'Test Task',
    hasEmail: false,
    hasDirectory: false,
    assignedTo: [{ role: mockRole }],
    emailTo: [],
    emailSubject: '',
    emailBody: '',
    triggerWorkflow: false,
    workflowToTrigger: null,
    serviceLevelAgreement: 24,
    serviceLevelAgreementWarning: 12,
    fields: [],
    workflow: {
      id: 'wf1',
      active: true,
      workflowType: {
        id: 'wt1',
        name: 'Test Workflow Type',
        type: {
          code: 'TT1',
          description: 'Test Type',
          data: [
            { field: 'field1', label: 'Field 1', isNumeric: false, values: [] },
            { field: 'itemId', label: 'Item ID', isNumeric: true, values: [] }
          ]
        }
      }
    }
  };

  const mockPaths = [
    {
      outputData: { taskName: 'Task1' },
      relevantDataField: 'field1',
      comparisonOperator: '=',
      relevantDataValue: 'value1',
      priority: 0
    },
    {
      outputData: { taskName: 'Task2' },
      relevantDataField: 'field2',
      comparisonOperator: null,
      relevantDataValue: 'value2',
      priority: 1
    }
  ];

  beforeEach(async () => {
    mockActivatedRoute = {
      params: of({ task: 'test-task' }),
      queryParams: of({ version: '1' }),
      snapshot: {
        params: { task: 'test-task' },
        queryParams: { version: '1' }
      }
    };

    mockRouter = {
      navigate: jest.fn()
    };

    mockEditingStateService = {
      setValue: jest.fn()
    };

    mockRoleService = {
      findAll: jest.fn().mockReturnValue(of([mockRole]))
    };

    mockWorkflowService = {
      findCompletedWorkflows: jest.fn().mockReturnValue(of([mockWorkflow])),
      findWorkflowTaskTypeByNameAndVersion: jest.fn().mockReturnValue(of(mockWorkflowTaskType)),
      findRelationsByTaskTypeInput: jest.fn().mockReturnValue(of(mockPaths)),
      updateWorkflowTaskTypeData: jest.fn().mockReturnValue(of({}))
    };

    mockToastService = {
      displayError: jest.fn(),
      displaySuccess: jest.fn()
    };

    mockDialogMessageService = {
      displayError: jest.fn()
    };

    mockLovService = {};

    mockAccountService = {
      hasAnyRole: jest.fn().mockReturnValue(true)
    };

    mockConfig = {
      business: {
        itemName: 'item',
        itemId: 'itemId'
      }
    };

    await TestBed.configureTestingModule({
      declarations: [TaskDefinitionComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: RoleService, useValue: mockRoleService },
        { provide: WorkflowService, useValue: mockWorkflowService },
        { provide: ToastService, useValue: mockToastService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: LovService, useValue: mockLovService },
        { provide: AccountService, useValue: mockAccountService },
        { provide: 'config', useValue: mockConfig }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(TaskDefinitionComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should be defined', () => {
      expect(component).toBeDefined();
    });

    it('should be an instance of TaskDefinitionComponent', () => {
      expect(component).toBeInstanceOf(TaskDefinitionComponent);
    });

    it('should initialize with default values', () => {
      expect(component.emailActiveIndex).toBe(-1);
      expect(component.relevantDataActiveIndex).toBe(-1);
      expect(component.allTasks).toEqual([]);
      expect(component.availableTasks).toEqual([]);
      expect(component.selectedTasks).toEqual([]);
      expect(component.errors).toEqual([]);
      expect(component.isEditing).toBe(false);
      expect(component.activeIndex).toBe(0);
      expect(component.activeStep).toBe('setting');
    });

    it('should have correct constants and properties', () => {
      expect(component.actions).toBe(RoleActions);
      expect(component.operators).toHaveLength(6);
      expect(component.singlePathItems).toHaveLength(3);
      expect(component.multiplePathItems).toHaveLength(4);
    });
  });
