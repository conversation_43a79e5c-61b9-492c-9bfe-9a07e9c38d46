import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError, Subject } from 'rxjs';
import { Node, Edge } from '@swimlane/ngx-graph';

import { WorkflowDefinitionComponent } from './workflow-definition.component';
import { ToastService } from '../../../../services/toast.service';
import { EditingStateService } from '../../../../services/editing-state.service';
import { WorkflowService } from '../../../../services/workflow/workflow.service';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { FormAction } from '../../../../models/form-action';
import { IWorkflow } from '../../../../models/workflow/workflow.model';
import { IWorkflowData } from '../../../../models/workflow/workflow-data.model';

// Mock grid utils
jest.mock('../../../../utils/grid-utils', () => ({
  handleCancelAction: jest.fn(() => of(null))
}));

describe('WorkflowDefinitionComponent', () => {
  let component: WorkflowDefinitionComponent;
  let fixture: ComponentFixture<WorkflowDefinitionComponent>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockWorkflowService: jest.Mocked<WorkflowService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: any;
  let editingStateSubject: Subject<FormAction>;

  const mockWorkflowType = {
    id: 'type1',
    name: 'Test Workflow Type',
    description: 'Test Description'
  };

  const mockWorkflow: IWorkflow = {
    id: 'workflow1',
    workflowType: mockWorkflowType,
    version: 1,
    active: true,
    completed: false
  };

  const mockWorkflows: IWorkflow[] = [
    mockWorkflow,
    {
      id: 'workflow2',
      workflowType: mockWorkflowType,
      version: 2,
      active: true,
      completed: false
    }
  ];

  const mockWorkflowData: IWorkflowData = {
    workflowId: 'workflow1',
    taskTypes: [
      { taskId: 'task1', taskName: 'Start', isDecision: false, description: 'Start', completed: true },
      { taskId: 'task2', taskName: 'Task 1', isDecision: false, description: 'Task 1', completed: false },
      { taskId: 'task3', taskName: 'End', isDecision: false, description: 'End', completed: true }
    ],
    relations: [
      { id: 1, workflowId: 'workflow1', input: 'Start', output: 'Task 1' },
      { id: 2, workflowId: 'workflow1', input: 'Task 1', output: 'End' }
    ]
  };

  beforeEach(async () => {
    editingStateSubject = new Subject<FormAction>();

    mockToastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn(),
      displayWarning: jest.fn(),
      displayInfo: jest.fn()
    } as any;

    mockEditingStateService = {
      getValue: jest.fn(() => editingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: { data: [], isEditing: false, url: {} }
    } as any;

    mockWorkflowService = {
      findWorkflowByName: jest.fn(),
      findWorkflowByVersionAndType: jest.fn(),
      findWorkflowsByType: jest.fn(),
      findById: jest.fn(),
      findWorkflowData: jest.fn(),
      updateWorkflowData: jest.fn()
    } as any;

    mockDialogMessageService = {
      displayError: jest.fn(),
      displaySuccess: jest.fn(),
      displayWarning: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn()
    } as any;

    mockActivatedRoute = {
      snapshot: {
        queryParams: {}
      }
    };

    await TestBed.configureTestingModule({
      declarations: [WorkflowDefinitionComponent],
      providers: [
        { provide: ToastService, useValue: mockToastService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: WorkflowService, useValue: mockWorkflowService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    // Setup default successful responses
    mockWorkflowService.findWorkflowByName.mockReturnValue(of(mockWorkflow));
    mockWorkflowService.findWorkflowsByType.mockReturnValue(of(mockWorkflows));
    mockWorkflowService.findWorkflowData.mockReturnValue(of(mockWorkflowData));
    mockWorkflowService.updateWorkflowData.mockReturnValue(of({}));

    fixture = TestBed.createComponent(WorkflowDefinitionComponent);
    component = fixture.componentInstance;

    // Mock window.location.pathname
    Object.defineProperty(window, 'location', {
      value: { pathname: '/admin/workflow/manage-workflow/test-workflow' },
      writable: true
    });
  });
