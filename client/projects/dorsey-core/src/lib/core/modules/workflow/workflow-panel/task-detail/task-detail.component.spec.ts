import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { UpperCasePipe } from '@angular/common';
import { of } from 'rxjs';

import { TaskDetailComponent } from './task-detail.component';
import { WorkflowService } from '../../../../services/workflow/workflow.service';
import { ToastService } from '../../../../services/toast.service';
import { UserService } from '../../../../services/admin/user.service';
import { RoleService } from '../../../../services/admin/role.service';
import { OrganizationService } from '../../../../services/admin/organization.service';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { EditingStateService } from '../../../../services/editing-state.service';
import { UploadedDataService } from '../../../../services/admin/uploaded-data.service';
import { LibraryService } from '../../../library/services/library.service';

describe('TaskDetailComponent', () => {
  let component: TaskDetailComponent;
  let fixture: ComponentFixture<TaskDetailComponent>;

  const mockWorkflowService = {
    findTaskById: jest.fn().mockReturnValue(of({
      taskType: {
        taskName: 'Test Task',
        workflow: {
          workflowType: {
            type: {
              data: []
            }
          }
        },
        hasDirectory: false
      },
      value: { caseId: 'test-case' },
      reqValue: { caseId: 'test-case' }
    })),
    approve: jest.fn().mockReturnValue(of({})),
    reject: jest.fn().mockReturnValue(of({})),
    next: jest.fn().mockReturnValue(of({}))
  };

  const mockActivatedRoute = {
    queryParams: of({ id: 'test-id', workflow: 'test-workflow' }),
    params: of({ id: 'test-id' }),
    snapshot: {
      queryParams: { id: 'test-id', workflow: 'test-workflow' },
      params: { id: 'test-id' }
    }
  };

  const mockToastService = {
    displaySuccess: jest.fn(),
    displayError: jest.fn(),
    displayInfo: jest.fn(),
    displayWarning: jest.fn()
  };

  const mockRouter = {
    navigate: jest.fn()
  };

  const mockUserService = {
    findUser: jest.fn().mockReturnValue(of({ id: 'test-user', name: 'Test User' }))
  };

  const mockRoleService = {
    getRoles: jest.fn().mockReturnValue(of([]))
  };

  const mockOrganizationService = {
    getOrganizations: jest.fn().mockReturnValue(of([]))
  };

  const mockDialogMessageService = {
    displayError: jest.fn(),
    displayCustom: jest.fn()
  };

  const mockEditingStateService = {
    isEditing: jest.fn().mockReturnValue(false)
  };

  const mockUploadedDataService = {
    uploadFile: jest.fn().mockReturnValue(of({}))
  };

  const mockLibraryService = {
    getFilesMetaData: jest.fn().mockReturnValue(of([]))
  };

  const mockHttpClient = {
    get: jest.fn().mockReturnValue(of({ name: 'test-item' }))
  };

  const mockUpperCasePipe = {
    transform: jest.fn()
  };

  const mockConfig = {
    environment: {
      apiUrl: 'http://test-api.com'
    },
    business: {
      endPoints: {
        dataByIdPath: 'test-path'
      },
      itemName: 'test-item'
    }
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskDetailComponent ],
      providers: [
        { provide: WorkflowService, useValue: mockWorkflowService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ToastService, useValue: mockToastService },
        { provide: Router, useValue: mockRouter },
        { provide: UserService, useValue: mockUserService },
        { provide: RoleService, useValue: mockRoleService },
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: UploadedDataService, useValue: mockUploadedDataService },
        { provide: LibraryService, useValue: mockLibraryService },
        { provide: HttpClient, useValue: mockHttpClient },
        { provide: UpperCasePipe, useValue: mockUpperCasePipe },
        { provide: 'config', useValue: mockConfig },
        FormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TaskDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
