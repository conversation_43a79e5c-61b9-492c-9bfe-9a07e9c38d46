import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, DebugElement } from '@angular/core';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { RouterTestingModule } from '@angular/router/testing';
import { By } from '@angular/platform-browser';

import { WorkflowComponent } from './workflow.component';

// Mock child components for testing
@Component({
  selector: 'workflow-panel-mock',
  template: '<div>Mock Workflow Panel</div>'
})
class MockWorkflowPanelComponent { }

@Component({
  selector: 'task-detail-mock',
  template: '<div>Mock Task Detail</div>'
})
class MockTaskDetailComponent { }

describe('WorkflowComponent', () => {
  let component: WorkflowComponent;
  let fixture: ComponentFixture<WorkflowComponent>;
  let router: Router;
  let location: Location;
  let debugElement: DebugElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        WorkflowComponent,
        MockWorkflowPanelComponent,
        MockTaskDetailComponent
      ],
      imports: [
        RouterTestingModule.withRoutes([
          { path: '', component: MockWorkflowPanelComponent },
          { path: 'task-detail', component: MockTaskDetailComponent }
        ])
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(WorkflowComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    location = TestBed.inject(Location);
    debugElement = fixture.debugElement;

    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should be defined', () => {
      expect(component).toBeDefined();
    });

    it('should be an instance of WorkflowComponent', () => {
      expect(component instanceof WorkflowComponent).toBe(true);
    });

    it('should have correct selector', () => {
      const compiled = fixture.nativeElement;
      expect(compiled).toBeDefined();
    });

    it('should initialize without errors', () => {
      expect(() => {
        const testComponent = new WorkflowComponent();
        testComponent.ngOnInit();
      }).not.toThrow();
    });
  });

  describe('Component Lifecycle', () => {
    it('should call ngOnInit', () => {
      const ngOnInitSpy = jest.spyOn(component, 'ngOnInit');

      component.ngOnInit();

      expect(ngOnInitSpy).toHaveBeenCalled();
    });

    it('should execute ngOnInit without errors', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });

    it('should be properly initialized after ngOnInit', () => {
      component.ngOnInit();

      expect(component).toBeDefined();
      expect(component.constructor).toBeDefined();
    });
  });

  describe('Template and DOM', () => {
    it('should render router-outlet', () => {
      const routerOutlet = debugElement.query(By.css('router-outlet'));
      expect(routerOutlet).toBeTruthy();
    });

    it('should have router-outlet as the main content', () => {
      const compiled = fixture.nativeElement;
      expect(compiled.querySelector('router-outlet')).toBeTruthy();
    });

    it('should contain only router-outlet in template', () => {
      const compiled = fixture.nativeElement;
      const children = compiled.children;
      expect(children.length).toBe(1);
      expect(children[0].tagName.toLowerCase()).toBe('router-outlet');
    });
  });

  describe('Router Integration', () => {
    it('should work with router navigation', async () => {
      await router.navigate(['']);
      expect(location.path()).toBe('');
    });

    it('should handle route changes', async () => {
      await router.navigate(['task-detail']);
      expect(location.path()).toBe('/task-detail');
    });

    it('should support child route navigation', async () => {
      await router.navigate(['']);
      fixture.detectChanges();

      expect(location.path()).toBe('');
    });
  });

  describe('Component Properties', () => {
    it('should have constructor defined', () => {
      expect(component.constructor).toBeDefined();
    });

    it('should implement OnInit interface', () => {
      expect(component.ngOnInit).toBeDefined();
      expect(typeof component.ngOnInit).toBe('function');
    });

    it('should not have any additional properties', () => {
      const expectedProperties = ['constructor', 'ngOnInit'];
      const actualProperties = Object.getOwnPropertyNames(Object.getPrototypeOf(component))
        .filter(prop => prop !== 'constructor');

      expect(actualProperties).toContain('ngOnInit');
    });
  });

  describe('Component Behavior', () => {
    it('should act as a routing container', () => {
      expect(component).toBeTruthy();
      expect(debugElement.query(By.css('router-outlet'))).toBeTruthy();
    });

    it('should not throw errors during component lifecycle', () => {
      expect(() => {
        component.ngOnInit();
        fixture.detectChanges();
      }).not.toThrow();
    });

    it('should maintain component state', () => {
      const initialComponent = component;
      fixture.detectChanges();

      expect(component).toBe(initialComponent);
    });
  });

  describe('Error Handling', () => {
    it('should handle component creation gracefully', () => {
      expect(() => {
        const newFixture = TestBed.createComponent(WorkflowComponent);
        newFixture.detectChanges();
      }).not.toThrow();
    });

    it('should handle multiple ngOnInit calls', () => {
      expect(() => {
        component.ngOnInit();
        component.ngOnInit();
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should handle fixture detection changes', () => {
      expect(() => {
        fixture.detectChanges();
        fixture.detectChanges();
      }).not.toThrow();
    });
  });

  describe('Integration Tests', () => {
    it('should work with TestBed configuration', () => {
      expect(TestBed.inject(Router)).toBeDefined();
      expect(TestBed.inject(Location)).toBeDefined();
    });

    it('should support component testing utilities', () => {
      expect(fixture.componentInstance).toBe(component);
      expect(fixture.debugElement).toBeDefined();
      expect(fixture.nativeElement).toBeDefined();
    });

    it('should handle component destruction', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });
});
