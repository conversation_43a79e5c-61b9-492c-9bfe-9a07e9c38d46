import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError, Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { UserListComponent } from './user-list.component';
import { UserService } from '../../../services/admin/user.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { ToastService } from '../../../services/toast.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { RoleService } from '../../../services/admin/role.service';
import { OrganizationService } from '../../../services/admin/organization.service';
import { FormAction } from '../../../models/form-action';
import { RoleActions } from '../../../models/enums/role-actions';
import { IRole } from '../../../models/admin/roles/role.model';
import { IUser } from '../../../models/admin/users/user.model';

// Mock utility functions
jest.mock('../../../utils/grid-utils', () => ({
  getGridData: jest.fn(),
  handleCancelAction: jest.fn(() => of({}))
}));

jest.mock('../../../utils/organization-hierarchy.util', () => ({
  addHierarchyLabelProperty: jest.fn(),
  addHierarchyLabelToNodes: jest.fn()
}));

describe('UserListComponent', () => {
  let component: UserListComponent;
  let fixture: ComponentFixture<UserListComponent>;
  let mockUserService: jest.Mocked<UserService>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockRoleService: jest.Mocked<RoleService>;
  let mockOrganizationService: jest.Mocked<OrganizationService>;

  // For tests that don't need template rendering
  let componentInstance: UserListComponent;

  const mockUsers: any[] = [
    {
      userId: '1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      title: 'Developer',
      isActive: true,
      effectiveDate: '2023-01-01',
      rowId: 0,
      roles: [{ roleId: '1', name: 'Admin', department: 'IT', description: 'Admin role', capabilities: [] }],
      hierarchyData: {
        id: 1,
        levelData: { hierarchyName: 'Department' },
        hierarchyValue: 'IT'
      }
    },
    {
      userId: '2',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      title: 'Manager',
      isActive: false,
      effectiveDate: '2023-01-01',
      rowId: 1,
      roles: [{ roleId: '2', name: 'User', department: 'HR', description: 'User role', capabilities: [] }],
      hierarchyData: {
        id: 2,
        levelData: { hierarchyName: 'Department' },
        hierarchyValue: 'HR'
      }
    }
  ];

  const mockRoles: IRole[] = [
    { roleId: '1', name: 'Admin', department: 'IT', description: 'Admin role', capabilities: [] },
    { roleId: '2', name: 'User', department: 'HR', description: 'User role', capabilities: [] },
    { roleId: '3', name: 'Visitor', department: 'System', description: 'Visitor role', capabilities: [] } // Should be filtered out
  ];

  const mockHierarchies = [
    {
      id: 1,
      levelData: { hierarchyName: 'Department' },
      hierarchyValue: 'IT',
      children: []
    },
    {
      id: 2,
      levelData: { hierarchyName: 'Department' },
      hierarchyValue: 'HR',
      children: []
    }
  ];

  beforeEach(async () => {
    const mockEditingStateSubject = new Subject<FormAction>();

    mockUserService = {
      findAll: jest.fn().mockReturnValue(of(mockUsers)),
      updateUsers: jest.fn().mockReturnValue(of({})),
      findLoggedUserInfo: jest.fn().mockReturnValue(of(mockUsers[0])),
      findUser: jest.fn().mockReturnValue(of(mockUsers[0])),
      updateUser: jest.fn().mockReturnValue(of({}))
    } as any;

    mockEditingStateService = {
      getValue: jest.fn().mockReturnValue(mockEditingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: {
        data: [],
        isEditing: false,
        formState: undefined,
        url: { prev: undefined, curr: undefined }
      },
      getEditingState: jest.fn(),
      getFormActionState: jest.fn(),
      getData: jest.fn(),
      clearData: jest.fn(),
      setPrevUrl: jest.fn(),
      getPrevUrl: jest.fn(),
      setCurrUrl: jest.fn(),
      getCurrUrl: jest.fn()
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayInfo: jest.fn(),
      displayWarning: jest.fn(),
      displayError: jest.fn()
    } as any;

    mockDialogMessageService = {
      sendMessage: jest.fn(),
      displayWarning: jest.fn(),
      displayError: jest.fn(),
      displayCustom: jest.fn(),
      message: of()
    } as any;

    mockRoleService = {
      findAll: jest.fn().mockReturnValue(of(mockRoles)),
      findById: jest.fn(),
      updateRoles: jest.fn().mockReturnValue(of({})),
      checkRolesExistence: jest.fn().mockReturnValue(of(true))
    } as any;

    mockOrganizationService = {
      findOrgTree: jest.fn().mockReturnValue(of(mockHierarchies)),
      findLevels: jest.fn().mockReturnValue(of([])),
      updateLevels: jest.fn().mockReturnValue(of({})),
      checkNodesExistence: jest.fn().mockReturnValue(of(true))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [UserListComponent],
      providers: [
        { provide: UserService, useValue: mockUserService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: ToastService, useValue: mockToastService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: RoleService, useValue: mockRoleService },
        { provide: OrganizationService, useValue: mockOrganizationService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    // Create component instance without template rendering for most tests
    componentInstance = new UserListComponent(
      mockUserService,
      mockEditingStateService,
      mockToastService,
      mockDialogMessageService,
      mockRoleService,
      mockOrganizationService
    );

    // For tests that need template rendering
    fixture = TestBed.createComponent(UserListComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(componentInstance).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(componentInstance.title).toBe('User List');
      expect(componentInstance.roles).toEqual([]);
      expect(componentInstance.hierarchies).toEqual([]);
      expect(componentInstance.errors).toEqual([]);
      expect(componentInstance.path).toBe(location.pathname);
      expect(componentInstance.actions).toBe(RoleActions);
    });

    it('should have required dependencies injected', () => {
      // Test that component was created successfully with all dependencies
      expect(componentInstance).toBeTruthy();
      expect(componentInstance.title).toBe('User List');
      expect(componentInstance.actions).toBe(RoleActions);
    });
  });

  describe('ngOnInit', () => {
    it('should call loadData on initialization', () => {
      jest.spyOn(componentInstance as any, 'loadData');

      componentInstance.ngOnInit();

      expect(componentInstance['loadData']).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should load users, roles, and hierarchies successfully', () => {
      const { addHierarchyLabelProperty, addHierarchyLabelToNodes } = jest.requireMock('../../../utils/organization-hierarchy.util');

      componentInstance['loadData']();

      expect(mockUserService.findAll).toHaveBeenCalled();
      expect(mockRoleService.findAll).toHaveBeenCalled();
      expect(mockOrganizationService.findOrgTree).toHaveBeenCalled();
      expect(addHierarchyLabelProperty).toHaveBeenCalledWith(mockUsers);
      expect(addHierarchyLabelToNodes).toHaveBeenCalledWith(mockHierarchies);
    });

    it('should filter out Visitor role', () => {
      componentInstance['loadData']();

      expect(componentInstance.roles).toEqual([
        { roleId: '1', name: 'Admin', department: 'IT', description: 'Admin role', capabilities: [] },
        { roleId: '2', name: 'User', department: 'HR', description: 'User role', capabilities: [] }
      ]);
    });

    it('should set rowData from users response', () => {
      componentInstance['loadData']();

      expect(componentInstance.rowData).toEqual(mockUsers);
    });

    it('should set hierarchies from organization response', () => {
      componentInstance['loadData']();

      expect(componentInstance.hierarchies).toEqual(mockHierarchies);
    });

    it('should call setColumnDefinition after loading data', () => {
      jest.spyOn(componentInstance as any, 'setColumnDefinition');

      componentInstance['loadData']();

      expect(componentInstance['setColumnDefinition']).toHaveBeenCalled();
    });
  });

  describe('Error Handling in Data Loading', () => {
    it('should handle user service error gracefully', () => {
      mockUserService.findAll.mockReturnValue(throwError('User service error'));

      componentInstance['loadData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Error occurred while loading users.');
    });

    it('should handle role service error gracefully', () => {
      mockRoleService.findAll.mockReturnValue(throwError('Role service error'));

      componentInstance['loadData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Error occurred while loading roles.');
    });

    it('should handle organization service error gracefully', () => {
      mockOrganizationService.findOrgTree.mockReturnValue(throwError('Organization service error'));

      componentInstance['loadData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Error occurred while loading Organization Hierarchy.');
    });
  });

  describe('Grid Operations', () => {
    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'api' } as any;

      componentInstance.onGridIsReady(mockGridApi);

      expect(componentInstance.gridApi).toBe(mockGridApi);
    });

    it('should return new row data with correct structure', () => {
      const newRow = componentInstance.onNewRow();

      expect(newRow).toEqual({
        firstName: '',
        lastName: '',
        title: '',
        email: '',
        roles: [],
        isActive: true,
      });
    });
  });

  describe('Column Definitions', () => {
    beforeEach(() => {
      componentInstance.roles = mockRoles.filter(r => r.name !== 'Visitor');
      componentInstance.hierarchies = mockHierarchies;
    });

    it('should set column definitions with correct structure', () => {
      componentInstance['setColumnDefinition']();

      expect(componentInstance.columnDefs).toBeDefined();
      expect(Array.isArray(componentInstance.columnDefs)).toBe(true);
      expect(componentInstance.columnDefs.length).toBeGreaterThan(0);
    });

    it('should include required columns', () => {
      componentInstance['setColumnDefinition']();

      const columnFields = componentInstance.columnDefs.map(col => col.field);
      expect(columnFields).toContain('firstName');
      expect(columnFields).toContain('lastName');
      expect(columnFields).toContain('email');
      expect(columnFields).toContain('title');
      expect(columnFields).toContain('roles');
      expect(columnFields).toContain('hierarchyData');
      expect(columnFields).toContain('isActive');
    });

    it('should configure roles column with correct options', () => {
      componentInstance['setColumnDefinition']();

      const rolesColumn = componentInstance.columnDefs.find(col => col.field === 'roles');
      expect(rolesColumn).toBeDefined();
      expect(rolesColumn.cellEditorParams.options).toEqual([
        { name: 'Admin', code: 'Admin' },
        { name: 'User', code: 'User' }
      ]);
    });

    it('should configure hierarchy column with correct options', () => {
      componentInstance['setColumnDefinition']();

      const hierarchyColumn = componentInstance.columnDefs.find(col => col.field === 'hierarchyData');
      expect(hierarchyColumn).toBeDefined();
      // The hierarchy column should exist, cellEditorParams may or may not be defined
      expect(hierarchyColumn.field).toBe('hierarchyData');
    });

    it('should configure status column with Active/Inactive options', () => {
      componentInstance['setColumnDefinition']();

      const statusColumn = componentInstance.columnDefs.find(col => col.field === 'isActive');
      expect(statusColumn).toBeDefined();
      expect(statusColumn.cellEditorParams.options).toEqual(['Active', 'Inactive']);
    });
  });

  describe('Data Validation', () => {
    beforeEach(() => {
      componentInstance.rowData = [...mockUsers];
      componentInstance.roles = mockRoles.filter(r => r.name !== 'Visitor');

      // Mock getGridData to return the rowData
      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue([...mockUsers]);
    });

    it('should validate data successfully with valid users', () => {
      // Ensure the mock data has all required fields in the correct format
      const validData = mockUsers.map(user => ({
        ...user,
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        email: user.email || '<EMAIL>',
        roles: user.roles || [{ name: 'Admin' }],
        effectiveDate: user.effectiveDate || '2023-01-01',
        isActive: user.isActive !== undefined ? user.isActive : true
      }));

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(validData);

      const result = componentInstance['validateData']();

      expect(result).toBe(true);
      expect(mockDialogMessageService.displayError).not.toHaveBeenCalled();
    });

    it('should detect missing first name', () => {
      const testData = [...mockUsers];
      testData[0].firstName = '';

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('First Name is required'),
        false
      );
    });

    it('should detect missing last name', () => {
      const testData = [...mockUsers];
      testData[0].lastName = '';

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('Last Name is required'),
        false
      );
    });

    it('should detect missing email', () => {
      const testData = [...mockUsers];
      testData[0].email = '';

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('Email is required'),
        false
      );
    });

    it('should detect invalid email format', () => {
      const testData = [...mockUsers];
      testData[0].email = 'invalid-email';

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('Email is invalid'),
        false
      );
    });

    it('should detect duplicate emails', () => {
      const testData = [...mockUsers];
      testData[1].email = testData[0].email;

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('is duplicated'),
        false
      );
    });

    it('should detect missing roles', () => {
      const testData = [...mockUsers];
      testData[0].roles = [];

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('Role is required'),
        false
      );
    });

    it('should detect missing effective date', () => {
      const testData = [...mockUsers];
      testData[0].effectiveDate = null;

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringContaining('Effective Date is required'),
        false
      );
    });

    it('should accumulate multiple validation errors', () => {
      const testData = [...mockUsers];
      testData[0].firstName = '';
      testData[0].email = '';
      testData[0].roles = [];

      const { getGridData } = jest.requireMock('../../../utils/grid-utils');
      getGridData.mockReturnValue(testData);

      const result = componentInstance['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        expect.stringMatching(/First Name is required.*Email is required.*Role is required/s),
        false
      );
    });
  });

  describe('Save Operations', () => {
    beforeEach(() => {
      componentInstance.rowData = [...mockUsers];
      componentInstance.roles = mockRoles.filter(r => r.name !== 'Visitor');
      jest.spyOn(componentInstance as any, 'validateData').mockReturnValue(true);
      jest.spyOn(componentInstance as any, 'loadData');
    });

    it('should save data successfully when validation passes', () => {
      componentInstance['saveData']();

      expect(componentInstance['validateData']).toHaveBeenCalled();
      expect(mockUserService.updateUsers).toHaveBeenCalledWith(componentInstance.rowData);
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Users saved successfully.');
      expect(componentInstance['loadData']).toHaveBeenCalled();
    });

    it('should not save data when validation fails', () => {
      (componentInstance as any)['validateData'].mockReturnValue(false);

      componentInstance['saveData']();

      expect(mockUserService.updateUsers).not.toHaveBeenCalled();
      expect(mockEditingStateService.setValue).not.toHaveBeenCalled();
      expect(mockToastService.displaySuccess).not.toHaveBeenCalled();
    });

    it('should process user data before saving', () => {
      // Ensure rowData has the correct structure with proper hierarchyData
      const testRowData = mockUsers.map(user => ({
        ...user,
        hierarchyData: {
          id: 1,
          levelData: { hierarchyName: 'Department' },
          hierarchyValue: 'IT'
        }
      }));
      componentInstance.rowData = testRowData;

      componentInstance['saveData']();

      // Check that the service was called (the actual data processing happens in the component)
      expect(mockUserService.updateUsers).toHaveBeenCalled();
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Users saved successfully.');
    });

    it('should filter roles correctly before saving', () => {
      // Ensure the user's role name matches one in the component's roles array
      componentInstance.rowData[0].roles = [{ roleId: '1', name: 'Admin', department: 'IT', description: 'Admin role', capabilities: [] }];

      componentInstance['saveData']();

      const savedData = mockUserService.updateUsers.mock.calls[0][0];
      expect(savedData[0].roles).toEqual([
        { roleId: '1', name: 'Admin', department: 'IT', description: 'Admin role', capabilities: [] }
      ]);
    });

    it('should handle save error gracefully', () => {
      mockUserService.updateUsers.mockReturnValue(throwError({ error: { error: 'Save failed' } }));

      componentInstance['saveData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Save failed');
    });
  });

  describe('Editing State Integration', () => {
    it('should handle SAVE action', () => {
      jest.spyOn(componentInstance as any, 'saveData');
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      const newComponent = new UserListComponent(
        mockUserService,
        mockEditingStateService,
        mockToastService,
        mockDialogMessageService,
        mockRoleService,
        mockOrganizationService
      );

      // Spy on the saveData method after component creation
      const saveDataSpy = jest.spyOn(newComponent as any, 'saveData');

      editingStateSubject.next(FormAction.SAVE);

      expect(saveDataSpy).toHaveBeenCalled();
    });

    it('should handle EDIT action', () => {
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      const newComponent = new UserListComponent(
        mockUserService,
        mockEditingStateService,
        mockToastService,
        mockDialogMessageService,
        mockRoleService,
        mockOrganizationService
      );

      newComponent.rowData = [...mockUsers];

      editingStateSubject.next(FormAction.EDIT);

      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['rowData', JSON.parse(JSON.stringify(newComponent.rowData))]
      ]);
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(componentInstance['destroy$'], 'next');
      const completeSpy = jest.spyOn(componentInstance['destroy$'], 'complete');

      componentInstance.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      const destroySpy = jest.spyOn(componentInstance['destroy$'], 'next');

      componentInstance.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty users response', () => {
      mockUserService.findAll.mockReturnValue(of([]));

      componentInstance['loadData']();

      expect(componentInstance.rowData).toEqual([]);
    });

    it('should handle empty roles response', () => {
      mockRoleService.findAll.mockReturnValue(of([]));

      componentInstance['loadData']();

      expect(componentInstance.roles).toEqual([]);
    });

    it('should handle empty hierarchies response', () => {
      mockOrganizationService.findOrgTree.mockReturnValue(of([]));

      componentInstance['loadData']();

      expect(componentInstance.hierarchies).toEqual([]);
    });

    it('should handle user data structure variations', () => {
      // Test that component can handle different user data structures
      const usersWithVariations = [
        { ...mockUsers[0], effectiveDate: null },
        { ...mockUsers[1], roles: [] }
      ];

      componentInstance.rowData = usersWithVariations;

      // Component should handle these variations without crashing
      expect(componentInstance.rowData).toBeDefined();
      expect(componentInstance.rowData.length).toBe(2);
    });

    it('should handle component state management', () => {
      // Test that component can manage its state properly
      componentInstance.rowData = mockUsers;
      componentInstance.roles = mockRoles.filter(r => r.name !== 'Visitor');
      componentInstance.hierarchies = mockHierarchies;

      // Component should maintain state correctly
      expect(componentInstance.rowData.length).toBe(2);
      expect(componentInstance.roles.length).toBe(2);
      expect(componentInstance.hierarchies.length).toBe(2);
    });
  });
});
