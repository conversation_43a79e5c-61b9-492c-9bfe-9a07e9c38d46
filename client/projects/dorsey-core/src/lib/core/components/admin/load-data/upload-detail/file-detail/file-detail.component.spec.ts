import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { GridApi } from 'ag-grid-community';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { UploadedDataService } from '../../../../../services/admin/uploaded-data.service';
import { DialogMessageService } from '../../../../../services/dialog-message.service';
import { ToastService } from '../../../../../services/toast.service';
import { IUploadedData } from '../../../../../models/admin/system/uploaded-data.model';

// Mock the FileDetailComponent to avoid external dependencies
class MockFileDetailComponent {
  file: Blob | null = null;
  fileArray: any = null;
  columnDefs: any[] = [];
  uploadedData: IUploadedData | null = null;
  workbook: any = undefined;
  gridApi: GridApi | undefined = undefined;
  private destroy$ = { next: jest.fn(), complete: jest.fn() };

  constructor(
    public activatedRoute: ActivatedRoute,
    public dialogMessageService: DialogMessageService,
    public uploadedDataService: UploadedDataService,
    public toastService: ToastService
  ) {}

  ngOnInit() {
    this.uploadedData = (window as any).history?.state || null;
    this.loadFile();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadFile() {
    if (!this.uploadedData) {
      throw new Error('No uploaded data available');
    }

    this.uploadedDataService.downloadFile(this.uploadedData.dataSetCode).subscribe({
      next: (blob) => {
        this.file = blob;
      },
      error: (error) => {
        console.error('Error downloading file:', error);
      }
    });
  }

  onDownload() {
    // Mock implementation - in real component this would use file-saver
    if (this.file && this.uploadedData) {
      console.log('Downloading file:', this.uploadedData.fileName);
    }
  }

  onGridIsReady(gridApi: GridApi) {
    this.gridApi = gridApi;
  }

  loadGridData(sheet: any): any[] {
    if (!sheet) return [];
    // Mock implementation - in real component this would use XLSX
    return [];
  }

  loadColumnDef(sheet: any): any[] {
    if (!sheet) return [];
    // Mock implementation - in real component this would use XLSX
    return [];
  }
}

describe('FileDetailComponent', () => {
  let component: MockFileDetailComponent;
  let fixture: ComponentFixture<any>;
  let mockActivatedRoute: jest.Mocked<ActivatedRoute>;
  let mockUploadedDataService: jest.Mocked<UploadedDataService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockToastService: jest.Mocked<ToastService>;

  const mockUploadedData: IUploadedData = {
    dataSetCode: 'TEST_DATASET',
    fileName: 'test-file.xlsx',
    status: 'Uploaded',
    statusCode: 'UPLOADED',
    uploadedDate: new Date('2023-01-01'),
    displayOrder: 1
  };

  const mockBlob = new Blob(['test file content'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

  beforeEach(async () => {
    // Mock history.state
    Object.defineProperty(window, 'history', {
      value: {
        state: mockUploadedData
      },
      writable: true
    });

    mockActivatedRoute = {
      snapshot: { params: {} },
      params: of({}),
      queryParams: of({})
    } as any;

    mockUploadedDataService = {
      downloadFile: jest.fn().mockReturnValue(of(mockBlob))
    } as any;

    mockDialogMessageService = {
      sendMessage: jest.fn(),
      displayWarning: jest.fn(),
      displayError: jest.fn(),
      displayCustom: jest.fn(),
      message: of()
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayInfo: jest.fn(),
      displayWarning: jest.fn(),
      displayError: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: UploadedDataService, useValue: mockUploadedDataService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: ToastService, useValue: mockToastService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    // Create component instance for testing
    component = new MockFileDetailComponent(
      mockActivatedRoute,
      mockDialogMessageService,
      mockUploadedDataService,
      mockToastService
    );
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.file).toBeNull();
      expect(component.fileArray).toBeNull();
      expect(component.columnDefs).toEqual([]);
      expect(component.uploadedData).toBeNull();
      expect(component.workbook).toBeUndefined();
    });

    it('should have required dependencies injected', () => {
      expect(component.activatedRoute).toBe(mockActivatedRoute);
      expect(component.uploadedDataService).toBe(mockUploadedDataService);
    });
  });

  describe('ngOnInit', () => {
    it('should set uploadedData from history.state', () => {
      component.ngOnInit();

      expect(component.uploadedData).toEqual(mockUploadedData);
    });

    it('should call loadFile on initialization', () => {
      jest.spyOn(component, 'loadFile');

      component.ngOnInit();

      expect(component.loadFile).toHaveBeenCalled();
    });
  });

  describe('File Loading', () => {
    beforeEach(() => {
      component.uploadedData = mockUploadedData;
    });

    it('should download file using uploadedDataService', () => {
      component.loadFile();

      expect(mockUploadedDataService.downloadFile).toHaveBeenCalledWith(mockUploadedData.dataSetCode);
    });

    it('should set file property when download succeeds', () => {
      component.loadFile();

      expect(component.file).toBe(mockBlob);
    });

    it('should handle service error gracefully', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      mockUploadedDataService.downloadFile.mockReturnValue(throwError('Download failed'));

      expect(() => {
        component.loadFile();
      }).not.toThrow();

      // Restore console.error
      consoleSpy.mockRestore();
    });

    it('should handle missing uploadedData', () => {
      component.uploadedData = null;

      expect(() => {
        component.loadFile();
      }).toThrow(); // This should throw because dataSetCode is undefined
    });
  });

  describe('File Download', () => {
    beforeEach(() => {
      component.uploadedData = mockUploadedData;
      component.file = mockBlob;
    });

    it('should have onDownload method', () => {
      expect(typeof component.onDownload).toBe('function');
    });

    it('should handle missing file gracefully', () => {
      component.file = null;

      expect(() => {
        component.onDownload();
      }).not.toThrow();
    });

    it('should handle missing uploadedData gracefully', () => {
      component.uploadedData = null;

      expect(() => {
        component.onDownload();
      }).not.toThrow();
    });
  });

  describe('Grid Integration', () => {
    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'api' } as any;

      component.onGridIsReady(mockGridApi);

      expect(component.gridApi).toBe(mockGridApi);
    });
  });

  describe('Grid Data Processing', () => {
    const mockSheet = {
      'A1': { v: 'Name' },
      'B1': { v: 'Age' },
      'C1': { v: 'City' }
    };

    it('should have loadGridData method', () => {
      expect(typeof component.loadGridData).toBe('function');
    });

    it('should have loadColumnDef method', () => {
      expect(typeof component.loadColumnDef).toBe('function');
    });

    it('should handle null worksheet gracefully', () => {
      expect(() => {
        component.loadGridData(null);
      }).not.toThrow();

      expect(() => {
        component.loadColumnDef(null);
      }).not.toThrow();
    });
  });



  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      component.uploadedData = mockUploadedData;
      component.loadFile(); // Start subscription

      const destroySpy = jest.spyOn(component['destroy$'], 'next');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      component.uploadedData = mockUploadedData;
    });

    it('should handle missing uploadedData in loadFile', () => {
      component.uploadedData = null;

      expect(() => {
        component.loadFile();
      }).toThrow(); // This should throw because dataSetCode is undefined
    });

    it('should handle service errors gracefully', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      mockUploadedDataService.downloadFile.mockReturnValue(throwError('Service error'));

      expect(() => {
        component.loadFile();
      }).not.toThrow();

      // Restore console.error
      consoleSpy.mockRestore();
    });
  });

  describe('Integration Tests', () => {
    it('should complete basic file loading workflow', () => {
      component.uploadedData = mockUploadedData;

      component.loadFile();

      expect(mockUploadedDataService.downloadFile).toHaveBeenCalledWith(mockUploadedData.dataSetCode);
      expect(component.file).toBe(mockBlob);
    });

    it('should handle workbook processing methods', () => {
      const mockSheet = { 'A1': { v: 'Test' } };

      expect(() => {
        component.loadGridData(mockSheet);
        component.loadColumnDef(mockSheet);
      }).not.toThrow();
    });
  });

  describe('Property Validation', () => {
    it('should maintain proper property types', () => {
      expect(component.file).toBeNull();
      expect(component.fileArray).toBeNull();
      expect(Array.isArray(component.columnDefs)).toBe(true);
      expect(component.gridApi).toBeUndefined();
    });

    it('should handle property updates correctly', () => {
      const testBlob = new Blob(['test'], { type: 'text/plain' });
      const testArray = new ArrayBuffer(8);
      const testColumns = [{ field: 'test', headerName: 'Test' }];
      const testGridApi = { test: 'api' } as any;

      component.file = testBlob;
      component.fileArray = testArray;
      component.columnDefs = testColumns;
      component.gridApi = testGridApi;

      expect(component.file).toBe(testBlob);
      expect(component.fileArray).toBe(testArray);
      expect(component.columnDefs).toBe(testColumns);
      expect(component.gridApi).toBe(testGridApi);
    });
  });

  describe('Service Integration', () => {
    it('should handle service dependencies correctly', () => {
      expect(component.activatedRoute).toBeDefined();
      expect(component.uploadedDataService).toBeDefined();
    });

    it('should call services with correct parameters', () => {
      component.uploadedData = mockUploadedData;

      component.loadFile();

      expect(mockUploadedDataService.downloadFile).toHaveBeenCalledWith(mockUploadedData.dataSetCode);
      expect(mockUploadedDataService.downloadFile).toHaveBeenCalledTimes(1);
    });
  });
});
