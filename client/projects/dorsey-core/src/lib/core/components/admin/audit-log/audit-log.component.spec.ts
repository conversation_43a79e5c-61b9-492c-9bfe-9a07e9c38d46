import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { GridApi } from 'ag-grid-community';
import { NO_ERRORS_SCHEMA, Component } from '@angular/core';

import { AuditLogComponent } from './audit-log.component';
import { UserService } from '../../../services/admin/user.service';
import { AuditLogService } from '../../../services/admin/audit-log.service';
import { IAuditLogFilter } from '../../../models/admin/audit-log/audit-log-filter.model';
import { AuditLog } from '../../../models/admin/audit-log/audit-log.model';
import { IUser } from '../../../models/admin/users/user.model';
import { DataGridMessage } from '../../datagrid/models/datagrid-message.model';
import { DatagridActionsCta } from '../../datagrid/models/enums/datagrid-actions-cta';
import { ActionState } from '../../datagrid/models/action-state';

// Create a test wrapper component to avoid template issues
@Component({
  template: '<div></div>'
})
class TestWrapperComponent {}

describe('AuditLogComponent', () => {
  let component: AuditLogComponent;
  let fixture: ComponentFixture<AuditLogComponent>;
  let mockRouter: jest.Mocked<Router>;
  let mockUserService: jest.Mocked<UserService>;
  let mockAuditLogService: jest.Mocked<AuditLogService>;

  // For tests that don't need template rendering
  let componentInstance: AuditLogComponent;

  const mockUsers: IUser[] = [
    {
      userId: 'user1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      isActive: true
    },
    {
      userId: 'user2',
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
      isActive: true
    }
  ];

  const mockFilter: IAuditLogFilter = {
    userEmails: ['<EMAIL>', '<EMAIL>'],
    actions: ['Create', 'Update', 'Delete'],
    entityTypes: ['User', 'Role', 'System'],
    entityKeys: ['key1', 'key2']
  };

  const mockAuditLogs: AuditLog[] = [
    {
      auditId: '1',
      userId: 'user1',
      actionCode: 'Create',
      entityCode: 'User',
      entityKey: 'user1',
      auditDate: '2023-01-01T10:00:00',
      entityValue: { type: 'string', value: '{"name":"John","email":"<EMAIL>"}', null: false }
    },
    {
      auditId: '2',
      userId: 'user2',
      actionCode: 'Update',
      entityCode: 'User',
      entityKey: 'user1',
      auditDate: '2023-01-02T11:00:00',
      entityValue: { type: 'string', value: '{"name":"John Updated","email":"<EMAIL>"}', null: false }
    },
    {
      auditId: '3',
      userId: 'user1',
      actionCode: 'Delete',
      entityCode: 'Role',
      entityKey: 'role1',
      auditDate: '2023-01-03T12:00:00',
      entityValue: { type: 'string', value: '{"name":"Admin Role","permissions":["read","write"]}', null: false }
    }
  ];

  beforeEach(async () => {
    mockRouter = {
      navigate: jest.fn()
    } as any;

    mockUserService = {
      findAll: jest.fn().mockReturnValue(of(mockUsers))
    } as any;

    mockAuditLogService = {
      findFilter: jest.fn().mockReturnValue(of(mockFilter)),
      findAuditLogs: jest.fn().mockReturnValue(of(mockAuditLogs))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [AuditLogComponent, TestWrapperComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: UserService, useValue: mockUserService },
        { provide: AuditLogService, useValue: mockAuditLogService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    // Create component instance without template rendering for most tests
    componentInstance = new AuditLogComponent(mockRouter, mockUserService, mockAuditLogService);

    // For tests that need template rendering
    fixture = TestBed.createComponent(AuditLogComponent);
    component = fixture.componentInstance;

    // Add the missing property that the template expects
    (component as any).auditLogList = {
      ACTION_STATE: ActionState
    };
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(componentInstance).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(componentInstance.auditLogs).toEqual([]);
      expect(componentInstance.displayDialog).toBe(false);
      expect(componentInstance.gridPageSize).toBe(25);
      expect(componentInstance.userEmail).toBe('All');
      expect(componentInstance.entityType).toBe('All');
      expect(componentInstance.action).toBe('All');
      expect(componentInstance.startDate).toBeDefined();
      expect(componentInstance.endDate).toBeDefined();
    });

    it('should have isExcel function available', () => {
      expect(componentInstance.isExcel).toBeDefined();
      expect(typeof componentInstance.isExcel).toBe('function');
    });
  });

  describe('ngOnInit', () => {
    it('should call loadUsers and loadFilters on initialization', () => {
      jest.spyOn(componentInstance as any, 'loadUsers');
      jest.spyOn(componentInstance as any, 'loadFilters');

      componentInstance.ngOnInit();

      expect(componentInstance['loadUsers']).toHaveBeenCalled();
      expect(componentInstance['loadFilters']).toHaveBeenCalled();
    });

    it('should load users and set column definitions', () => {
      componentInstance.ngOnInit();

      expect(mockUserService.findAll).toHaveBeenCalled();
      expect(componentInstance['users']).toEqual(mockUsers);
      expect(componentInstance.columnDefs).toBeDefined();
      expect(componentInstance.columnDefs.length).toBe(6); // Updated to 6 columns including actions
    });

    it('should load filters', () => {
      componentInstance.ngOnInit();

      expect(mockAuditLogService.findFilter).toHaveBeenCalled();
      expect(componentInstance.filter).toEqual(mockFilter);
    });
  });

  describe('Column Definitions', () => {
    beforeEach(() => {
      componentInstance.ngOnInit();
    });

    it('should configure audit date column with date formatter', () => {
      const dateColumn = componentInstance.columnDefs[0];

      expect(dateColumn.field).toBe('auditDate');
      expect(dateColumn.headerName).toBe('Date');
      expect(dateColumn.valueFormatter).toBeDefined();
    });

    it('should format audit date values correctly', () => {
      const dateColumn = componentInstance.columnDefs[0];
      const testDate = '2023-01-15T14:30:00';

      const formattedDate = dateColumn.valueFormatter({ value: testDate });
      expect(formattedDate).toBe('01/15/2023 14:30:00');
    });

    it('should handle null audit date values', () => {
      const dateColumn = componentInstance.columnDefs[0];

      const formattedDate = dateColumn.valueFormatter({ value: null });
      expect(formattedDate).toBeNull();
    });

    it('should configure user column with user email formatter', () => {
      const userColumn = componentInstance.columnDefs[1];

      expect(userColumn.field).toBe('userId');
      expect(userColumn.headerName).toBe('User');
      expect(userColumn.valueFormatter).toBeDefined();
    });

    it('should format user ID to email correctly', () => {
      const userColumn = componentInstance.columnDefs[1];

      const formattedUser = userColumn.valueFormatter({ value: 'user1' });
      expect(formattedUser).toBe('<EMAIL>');
    });

    it('should return original value if user not found', () => {
      const userColumn = componentInstance.columnDefs[1];

      const formattedUser = userColumn.valueFormatter({ value: 'unknown-user' });
      expect(formattedUser).toBe('unknown-user');
    });

    it('should configure action code column', () => {
      const actionColumn = componentInstance.columnDefs[2];

      expect(actionColumn.field).toBe('actionCode');
      expect(actionColumn.headerName).toBe('Action Taken');
    });

    it('should configure entity code column', () => {
      const entityColumn = componentInstance.columnDefs[3];

      expect(entityColumn.field).toBe('entityCode');
      expect(entityColumn.headerName).toBe('Entity Type');
    });

    it('should configure entity key column', () => {
      const entityKeyColumn = componentInstance.columnDefs[4];

      expect(entityKeyColumn.field).toBe('entityKey');
    });

    it('should configure actions column with action cell renderer', () => {
      const actionsColumn = componentInstance.columnDefs[5];

      expect(actionsColumn.headerName).toBe('Actions');
      expect(actionsColumn.field).toBe('action');
      expect(actionsColumn.editable).toBe(false);
      expect(actionsColumn.sortable).toBe(false);
      expect(actionsColumn.cellRenderer).toBeDefined();
    });
  });

  describe('Grid Integration', () => {
    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'api' } as any;

      componentInstance.onGridIsReady(mockGridApi);

      expect(componentInstance.gridApi).toBe(mockGridApi);
    });
  });

  describe('Data Loading', () => {
    beforeEach(() => {
      componentInstance.ngOnInit();
    });

    it('should load audit logs when onRun is called', () => {
      jest.spyOn(componentInstance as any, 'loadData');

      componentInstance.onRun();

      expect(componentInstance['loadData']).toHaveBeenCalled();
    });

    it('should call audit log service with correct parameters', () => {
      componentInstance.userEmail = '<EMAIL>';
      componentInstance.action = 'Update';
      componentInstance.entityType = 'User';
      componentInstance.startDate = '01/01/2023';
      componentInstance.endDate = '01/31/2023';

      componentInstance.onRun();

      expect(mockAuditLogService.findAuditLogs).toHaveBeenCalledWith(
        '<EMAIL>',
        'Update',
        'User',
        '01/01/2023',
        '01/31/2023'
      );
    });

    it('should use default values when parameters are null', () => {
      componentInstance.userEmail = null;
      componentInstance.action = null;
      componentInstance.entityType = null;

      componentInstance.onRun();

      expect(mockAuditLogService.findAuditLogs).toHaveBeenCalledWith(
        'All',
        'All',
        'All',
        componentInstance.startDate,
        componentInstance.endDate
      );
    });

    it('should transform audit logs correctly', () => {
      componentInstance.onRun();

      expect(componentInstance.auditLogs.length).toBe(3);
      expect(componentInstance.auditLogs[0]).toEqual(
        expect.objectContaining({
          auditId: '1',
          userId: 'user1',
          actionCode: 'Create',
          action: { ...ActionState.VIEW, canView: false },
          isEditing: false,
          isDisabled: false,
          rowId: 0
        })
      );
    });

    it('should set VIEW action for Update, Delete, and Upload actions', () => {
      const updateLog = { ...mockAuditLogs[0], actionCode: 'Update' };
      const deleteLog = { ...mockAuditLogs[0], actionCode: 'Delete' };
      const uploadLog = { ...mockAuditLogs[0], actionCode: 'Upload' };

      mockAuditLogService.findAuditLogs.mockReturnValue(of([updateLog, deleteLog, uploadLog]));

      componentInstance.onRun();

      expect(componentInstance.auditLogs[0].action).toEqual(ActionState.VIEW);
      expect(componentInstance.auditLogs[1].action).toEqual(ActionState.VIEW);
      expect(componentInstance.auditLogs[2].action).toEqual(ActionState.VIEW);
    });

    it('should set disabled VIEW action for other actions', () => {
      const createLog = { ...mockAuditLogs[0], actionCode: 'Create' };
      mockAuditLogService.findAuditLogs.mockReturnValue(of([createLog]));

      componentInstance.onRun();

      expect(componentInstance.auditLogs[0].action).toEqual({ ...ActionState.VIEW, canView: false });
    });

    it('should handle service error gracefully', () => {
      mockAuditLogService.findAuditLogs.mockReturnValue(throwError('Service error'));

      expect(() => {
        componentInstance.onRun();
      }).not.toThrow();
    });
  });

  describe('Cell Actions', () => {
    beforeEach(() => {
      componentInstance.auditLogs = mockAuditLogs.map(log => ({
        ...log,
        action: ActionState.VIEW,
        isEditing: false,
        isDisabled: false,
        rowId: 0
      }));
    });

    it('should handle VIEW action', () => {
      const message: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: mockAuditLogs[0],
        messageType: 'test',
        rendererName: 'test',
        rowIndex: 0,
        arrayIndex: 0,
        visibleRowIndex: 0,
        columnIndex: 0,
        columnId: 'test',
        rowData: mockAuditLogs[0],
        gridId: 'test'
      };

      jest.spyOn(componentInstance as any, 'onView');

      componentInstance.onCellAction(message);

      expect(componentInstance['onView']).toHaveBeenCalledWith(message);
    });

    it('should ignore unknown actions', () => {
      const message: DataGridMessage = {
        action: DatagridActionsCta.EDIT,
        rawData: mockAuditLogs[0],
        messageType: 'test',
        rendererName: 'test',
        rowIndex: 0,
        arrayIndex: 0,
        visibleRowIndex: 0,
        columnIndex: 0,
        columnId: 'test',
        rowData: mockAuditLogs[0],
        gridId: 'test'
      };

      jest.spyOn(componentInstance as any, 'onView');

      componentInstance.onCellAction(message);

      expect(componentInstance['onView']).not.toHaveBeenCalled();
    });
  });

  describe('View Dialog', () => {
    beforeEach(() => {
      componentInstance.auditLogs = mockAuditLogs.map(log => ({
        ...log,
        action: ActionState.VIEW,
        isEditing: false,
        isDisabled: false,
        rowId: 0
      }));
    });

    it('should open dialog when viewing audit log', () => {
      const message: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: mockAuditLogs[0],
        messageType: 'test',
        rendererName: 'test',
        rowIndex: 0,
        arrayIndex: 0,
        visibleRowIndex: 0,
        columnIndex: 0,
        columnId: 'test',
        rowData: mockAuditLogs[0],
        gridId: 'test'
      };

      componentInstance['onView'](message);

      expect(componentInstance.auditLog).toBe(mockAuditLogs[0]);
      expect(componentInstance.displayDialog).toBe(true);
    });

    it('should load previous value for Update actions', () => {
      const updateMessage: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: { ...mockAuditLogs[1], actionCode: 'Update' },
        messageType: 'test',
        rendererName: 'test',
        rowIndex: 0,
        arrayIndex: 0,
        visibleRowIndex: 0,
        columnIndex: 0,
        columnId: 'test',
        rowData: mockAuditLogs[1],
        gridId: 'test'
      };

      jest.spyOn(componentInstance as any, 'loadPrevValue');

      componentInstance['onView'](updateMessage);

      expect(componentInstance['loadPrevValue']).toHaveBeenCalledWith(updateMessage.rawData);
    });

    it('should not load previous value for non-Update actions', () => {
      const createMessage: DataGridMessage = {
        action: DatagridActionsCta.VIEW,
        rawData: { ...mockAuditLogs[0], actionCode: 'Create' },
        messageType: 'test',
        rendererName: 'test',
        rowIndex: 0,
        arrayIndex: 0,
        visibleRowIndex: 0,
        columnIndex: 0,
        columnId: 'test',
        rowData: mockAuditLogs[0],
        gridId: 'test'
      };

      jest.spyOn(componentInstance as any, 'loadPrevValue');

      componentInstance['onView'](createMessage);

      expect(componentInstance['loadPrevValue']).not.toHaveBeenCalled();
    });
  });

  describe('Entity Value Processing', () => {
    it('should return null for null audit log', () => {
      const result = componentInstance.getEntityValue(null);
      expect(result).toBeNull();
    });

    it('should return null for audit log with null entity value', () => {
      const auditLog: AuditLog = {
        auditId: '1',
        entityValue: { type: 'string', value: '', null: true }
      };

      const result = componentInstance.getEntityValue(auditLog);
      expect(result).toBeNull();
    });

    it('should parse entity value JSON correctly', () => {
      const auditLog: AuditLog = {
        auditId: '1',
        entityValue: {
          type: 'string',
          value: '{"name":"John","email":"<EMAIL>"}',
          null: false
        }
      };

      const result = componentInstance.getEntityValue(auditLog);
      expect(result).toEqual({ name: 'John', email: '<EMAIL>' });
    });

    it('should handle entity value with id property', () => {
      const auditLog: AuditLog = {
        auditId: '1',
        entityValue: {
          type: 'string',
          value: '{"id":{"userId":"123","name":"John"},"email":"<EMAIL>"}',
          null: false
        }
      };

      const result = componentInstance.getEntityValue(auditLog);
      expect(result).toEqual({
        userId: '123',
        name: 'John',
        email: '<EMAIL>'
      });
      expect(result.id).toBeUndefined();
    });

    it('should handle malformed JSON gracefully', () => {
      const auditLog: AuditLog = {
        auditId: '1',
        entityValue: {
          type: 'string',
          value: 'invalid json',
          null: false
        }
      };

      expect(() => {
        componentInstance.getEntityValue(auditLog);
      }).toThrow();
    });
  });

  describe('Previous Value Loading', () => {
    beforeEach(() => {
      componentInstance.auditLogs = [
        {
          ...mockAuditLogs[0],
          auditDate: '2023-01-01T10:00:00',
          entityCode: 'User',
          entityKey: 'user1',
          action: ActionState.VIEW,
          isEditing: false,
          isDisabled: false,
          rowId: 0
        },
        {
          ...mockAuditLogs[1],
          auditDate: '2023-01-02T11:00:00',
          entityCode: 'User',
          entityKey: 'user1',
          action: ActionState.VIEW,
          isEditing: false,
          isDisabled: false,
          rowId: 1
        },
        {
          ...mockAuditLogs[2],
          auditDate: '2023-01-03T12:00:00',
          entityCode: 'Role',
          entityKey: 'role1',
          action: ActionState.VIEW,
          isEditing: false,
          isDisabled: false,
          rowId: 2
        }
      ];
    });

    it('should find previous audit log for same entity', () => {
      const rawData = {
        entityCode: 'User',
        entityKey: 'user1',
        auditDate: '2023-01-02T11:00:00'
      };

      componentInstance['loadPrevValue'](rawData);

      expect(componentInstance.prevAuditLog).toEqual(componentInstance.auditLogs[0]);
    });

    it('should not find previous audit log for different entity', () => {
      const rawData = {
        entityCode: 'Role',
        entityKey: 'role1',
        auditDate: '2023-01-03T12:00:00'
      };

      componentInstance['loadPrevValue'](rawData);

      expect(componentInstance.prevAuditLog).toBeUndefined();
    });

    it('should not find previous audit log for first entry', () => {
      const rawData = {
        entityCode: 'User',
        entityKey: 'user1',
        auditDate: '2023-01-01T10:00:00'
      };

      componentInstance['loadPrevValue'](rawData);

      expect(componentInstance.prevAuditLog).toBeUndefined();
    });

    it('should handle empty audit logs array', () => {
      componentInstance.auditLogs = [];
      const rawData = {
        entityCode: 'User',
        entityKey: 'user1',
        auditDate: '2023-01-01T10:00:00'
      };

      componentInstance['loadPrevValue'](rawData);

      expect(componentInstance.prevAuditLog).toBeUndefined();
    });
  });

  describe('Service Error Handling', () => {
    it('should handle user service error gracefully', () => {
      mockUserService.findAll.mockReturnValue(throwError('User service error'));

      expect(() => {
        componentInstance.ngOnInit();
      }).not.toThrow();
    });

    it('should handle audit log service filter error gracefully', () => {
      mockAuditLogService.findFilter.mockReturnValue(throwError('Filter service error'));

      expect(() => {
        componentInstance.ngOnInit();
      }).not.toThrow();
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(componentInstance['destroy$'], 'next');
      const completeSpy = jest.spyOn(componentInstance['destroy$'], 'complete');

      componentInstance.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      componentInstance.ngOnInit(); // Start subscriptions

      const destroySpy = jest.spyOn(componentInstance['destroy$'], 'next');

      componentInstance.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Filter Properties', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should initialize filter properties with default values', () => {
      expect(component.userEmail).toBe('All');
      expect(component.entityType).toBe('All');
      expect(component.action).toBe('All');
      expect(component.startDate).toBeDefined();
      expect(component.endDate).toBeDefined();
    });

    it('should format dates correctly', () => {
      const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
      expect(component.startDate).toMatch(dateRegex);
      expect(component.endDate).toMatch(dateRegex);
    });

    it('should allow updating filter properties', () => {
      component.userEmail = '<EMAIL>';
      component.entityType = 'User';
      component.action = 'Update';
      component.startDate = '01/01/2023';
      component.endDate = '01/31/2023';

      expect(component.userEmail).toBe('<EMAIL>');
      expect(component.entityType).toBe('User');
      expect(component.action).toBe('Update');
      expect(component.startDate).toBe('01/01/2023');
      expect(component.endDate).toBe('01/31/2023');
    });
  });

  describe('Grid Configuration', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should have correct grid page size', () => {
      expect(component.gridPageSize).toBe(25);
    });

    it('should initialize with empty audit logs', () => {
      expect(component.auditLogs).toEqual([]);
    });

    it('should have search value property', () => {
      expect(component.searchValue).toBeUndefined();

      component.searchValue = 'test search';
      expect(component.searchValue).toBe('test search');
    });
  });
});
