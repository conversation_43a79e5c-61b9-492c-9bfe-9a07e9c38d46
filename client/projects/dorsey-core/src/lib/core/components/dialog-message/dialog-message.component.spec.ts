import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TemplateRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

import { DialogMessageComponent } from './dialog-message.component';
import { DialogMessageService } from '../../services/dialog-message.service';
import { DialogMessage } from '../../models/dialog-message.model';

describe('DialogMessageComponent', () => {
  let component: DialogMessageComponent;
  let fixture: ComponentFixture<DialogMessageComponent>;
  let dialogMessageService: DialogMessageService;

  const mockDialogMessage: DialogMessage = {
    title: 'Test Title',
    body: 'Test message body',
    ctaLabel: 'OK',
    okCallBack: jest.fn(),
    cancelCallBack: jest.fn(),
    hasCancel: true
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogMessageComponent],
      providers: [
        DialogMessageService
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DialogMessageComponent);
    component = fixture.componentInstance;
    dialogMessageService = TestBed.inject(DialogMessageService);
  });

  afterEach(() => {
    // Clean up any active subscriptions
    if (component?.subscription) {
      component.subscription.unsubscribe();
    }
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with undefined message and customBody', () => {
      expect(component.message).toBeUndefined();
      expect(component.customBody).toBeUndefined();
    });

    it('should subscribe to dialog message service on construction', () => {
      expect(component.subscription).toBeDefined();
      expect(dialogMessageService.message).toBeDefined();
    });

    it('should have required properties', () => {
      expect(component.message).toBeUndefined();
      expect(component.subscription).toBeDefined();
      expect(component.customBody).toBeUndefined();
    });
  });

  describe('Message Subscription', () => {
    it('should receive and set message when service sends a message', () => {
      const testMessage = { ...mockDialogMessage };

      dialogMessageService.sendMessage(testMessage);

      expect(component.message).toEqual(testMessage);
    });

    it('should handle string body messages', () => {
      const stringMessage: DialogMessage = {
        ...mockDialogMessage,
        body: 'Simple string message'
      };

      dialogMessageService.sendMessage(stringMessage);

      expect(component.message.body).toBe('Simple string message');
      expect(component.customBody).toBeUndefined();
    });

    it('should handle TemplateRef body messages', () => {
      // Create a proper TemplateRef mock that extends TemplateRef
      class MockTemplateRef extends TemplateRef<any> {
        createEmbeddedView = jest.fn();
        elementRef = { nativeElement: {} };
        injector = {};
      }
      const mockTemplateRef = new MockTemplateRef();
      const templateMessage: DialogMessage = {
        ...mockDialogMessage,
        body: mockTemplateRef
      };

      dialogMessageService.sendMessage(templateMessage);

      expect(component.message.body).toBe(mockTemplateRef);
      expect(component.customBody).toBe(mockTemplateRef);
    });

    it('should handle SafeHtml body messages', () => {
      const mockSafeHtml = { changingThisBreaksApplicationSecurity: '<p>Safe HTML</p>' } as SafeHtml;
      const safeHtmlMessage: DialogMessage = {
        ...mockDialogMessage,
        body: mockSafeHtml
      };

      dialogMessageService.sendMessage(safeHtmlMessage);

      expect(component.message.body).toBe(mockSafeHtml);
      expect(component.customBody).toBeUndefined();
    });
  });

  describe('ngOnInit', () => {
    it('should execute without errors', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });

    it('should not affect existing subscriptions', () => {
      const originalSubscription = component.subscription;

      component.ngOnInit();

      expect(component.subscription).toBe(originalSubscription);
    });
  });

  describe('onCancel Method', () => {
    beforeEach(() => {
      component.message = { ...mockDialogMessage };
      component.customBody = {} as TemplateRef<any>;
    });

    it('should call cancelCallBack when message has cancelCallBack', () => {
      const cancelSpy = jest.fn();
      component.message.cancelCallBack = cancelSpy;

      component.onCancel();

      expect(cancelSpy).toHaveBeenCalled();
    });

    it('should not throw error when message has no cancelCallBack', () => {
      component.message.cancelCallBack = undefined;

      expect(() => component.onCancel()).not.toThrow();
    });

    it('should clear message after cancel', () => {
      component.onCancel();

      expect(component.message).toBeUndefined();
    });

    it('should clear customBody after cancel', () => {
      component.onCancel();

      expect(component.customBody).toBeUndefined();
    });

    it('should handle null cancelCallBack', () => {
      component.message.cancelCallBack = null;

      expect(() => component.onCancel()).not.toThrow();
      expect(component.message).toBeUndefined();
    });

    it('should call cancelCallBack and then clear properties', () => {
      const cancelSpy = jest.fn();
      component.message.cancelCallBack = cancelSpy;

      component.onCancel();

      expect(cancelSpy).toHaveBeenCalled();
      expect(component.message).toBeUndefined();
      expect(component.customBody).toBeUndefined();
    });
  });

  describe('onAccept Method', () => {
    beforeEach(() => {
      component.message = { ...mockDialogMessage };
      component.customBody = {} as TemplateRef<any>;
    });

    it('should call okCallBack when message has okCallBack', () => {
      const okSpy = jest.fn();
      component.message.okCallBack = okSpy;

      component.onAccept();

      expect(okSpy).toHaveBeenCalled();
    });

    it('should not throw error when message has no okCallBack', () => {
      component.message.okCallBack = undefined;

      expect(() => component.onAccept()).not.toThrow();
    });

    it('should clear message after accept', () => {
      component.onAccept();

      expect(component.message).toBeUndefined();
    });

    it('should clear customBody after accept', () => {
      component.onAccept();

      expect(component.customBody).toBeUndefined();
    });

    it('should handle null okCallBack', () => {
      component.message.okCallBack = null;

      expect(() => component.onAccept()).not.toThrow();
      expect(component.message).toBeUndefined();
    });

    it('should call okCallBack and then clear properties', () => {
      const okSpy = jest.fn();
      component.message.okCallBack = okSpy;

      component.onAccept();

      expect(okSpy).toHaveBeenCalled();
      expect(component.message).toBeUndefined();
      expect(component.customBody).toBeUndefined();
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscription', () => {
      const unsubscribeSpy = jest.spyOn(component.subscription, 'unsubscribe');

      component.ngOnDestroy();

      expect(unsubscribeSpy).toHaveBeenCalled();
    });

    it('should not throw error if subscription is already unsubscribed', () => {
      component.subscription.unsubscribe();

      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should handle multiple calls to ngOnDestroy', () => {
      component.ngOnDestroy();

      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('Message Types Integration', () => {
    it('should handle warning messages correctly', () => {
      const warningMessage: DialogMessage = {
        title: 'Warning',
        body: 'This is a warning message',
        ctaLabel: 'Continue',
        okCallBack: jest.fn(),
        cancelCallBack: jest.fn(),
        hasCancel: true
      };

      dialogMessageService.sendMessage(warningMessage);

      expect(component.message.title).toBe('Warning');
      expect(component.message.ctaLabel).toBe('Continue');
      expect(component.message.hasCancel).toBe(true);
    });

    it('should handle error messages correctly', () => {
      const errorMessage: DialogMessage = {
        title: 'Error',
        body: 'This is an error message',
        ctaLabel: 'OK',
        okCallBack: jest.fn(),
        cancelCallBack: jest.fn(),
        hasCancel: false
      };

      dialogMessageService.sendMessage(errorMessage);

      expect(component.message.title).toBe('Error');
      expect(component.message.ctaLabel).toBe('OK');
      expect(component.message.hasCancel).toBe(false);
    });

    it('should handle custom messages with TemplateRef', () => {
      // Create a proper TemplateRef mock that extends TemplateRef
      class MockTemplateRef extends TemplateRef<any> {
        createEmbeddedView = jest.fn();
        elementRef = { nativeElement: {} };
        injector = {};
      }
      const mockTemplateRef = new MockTemplateRef();
      const customMessage: DialogMessage = {
        title: 'Custom Dialog',
        body: mockTemplateRef,
        ctaLabel: 'Proceed',
        okCallBack: jest.fn(),
        cancelCallBack: jest.fn(),
        hasCancel: true
      };

      dialogMessageService.sendMessage(customMessage);

      expect(component.message.title).toBe('Custom Dialog');
      expect(component.message.body).toBe(mockTemplateRef);
      expect(component.customBody).toBe(mockTemplateRef);
      expect(component.message.ctaLabel).toBe('Proceed');
    });
  });

  describe('Edge Cases', () => {
    it('should handle message with undefined title', () => {
      const messageWithoutTitle: DialogMessage = {
        title: undefined,
        body: 'Message without title',
        ctaLabel: 'OK',
        okCallBack: jest.fn(),
        cancelCallBack: jest.fn(),
        hasCancel: false
      };

      dialogMessageService.sendMessage(messageWithoutTitle);

      expect(component.message.title).toBeUndefined();
      expect(component.message.body).toBe('Message without title');
    });

    it('should handle message with empty string body', () => {
      const messageWithEmptyBody: DialogMessage = {
        title: 'Empty Body',
        body: '',
        ctaLabel: 'OK',
        okCallBack: jest.fn(),
        cancelCallBack: jest.fn(),
        hasCancel: false
      };

      dialogMessageService.sendMessage(messageWithEmptyBody);

      expect(component.message.body).toBe('');
      expect(component.customBody).toBeUndefined();
    });

    it('should handle message with undefined callbacks', () => {
      const messageWithoutCallbacks: DialogMessage = {
        title: 'No Callbacks',
        body: 'Message without callbacks',
        ctaLabel: 'OK',
        okCallBack: undefined,
        cancelCallBack: undefined,
        hasCancel: false
      };

      // Test onAccept with undefined callback
      component.message = { ...messageWithoutCallbacks };
      expect(() => component.onAccept()).not.toThrow();
      expect(component.message).toBeUndefined();

      // Test onCancel with undefined callback
      component.message = { ...messageWithoutCallbacks };
      expect(() => component.onCancel()).not.toThrow();
      expect(component.message).toBeUndefined();
    });

    it('should handle rapid message changes', () => {
      const message1 = { ...mockDialogMessage, title: 'Message 1' };
      const message2 = { ...mockDialogMessage, title: 'Message 2' };

      dialogMessageService.sendMessage(message1);
      expect(component.message.title).toBe('Message 1');

      dialogMessageService.sendMessage(message2);
      expect(component.message.title).toBe('Message 2');
    });
  });

  describe('Component Integration with Template', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should have message property accessible', () => {
      component.message = mockDialogMessage;
      fixture.detectChanges();

      expect(component.message).toEqual(mockDialogMessage);
    });

    it('should clear message when undefined is set', () => {
      component.message = mockDialogMessage;
      component.message = undefined;
      fixture.detectChanges();

      expect(component.message).toBeUndefined();
    });

    it('should handle message property changes', () => {
      const message1 = { ...mockDialogMessage, title: 'First' };
      const message2 = { ...mockDialogMessage, title: 'Second' };

      component.message = message1;
      expect(component.message.title).toBe('First');

      component.message = message2;
      expect(component.message.title).toBe('Second');
    });

    it('should have onAccept method available', () => {
      expect(component.onAccept).toBeDefined();
      expect(typeof component.onAccept).toBe('function');
    });

    it('should have onCancel method available', () => {
      expect(component.onCancel).toBeDefined();
      expect(typeof component.onCancel).toBe('function');
    });
  });

  describe('Service Integration', () => {
    it('should receive messages from DialogMessageService', () => {
      const testMessage = { ...mockDialogMessage };

      dialogMessageService.sendMessage(testMessage);

      expect(component.message).toEqual(testMessage);
    });

    it('should handle multiple service messages', () => {
      const message1 = { ...mockDialogMessage, title: 'First Message' };
      const message2 = { ...mockDialogMessage, title: 'Second Message' };

      dialogMessageService.sendMessage(message1);
      expect(component.message.title).toBe('First Message');

      dialogMessageService.sendMessage(message2);
      expect(component.message.title).toBe('Second Message');
    });
  });

  describe('Memory Management', () => {
    it('should properly clean up subscription on destroy', () => {
      const subscription = component.subscription;
      const unsubscribeSpy = jest.spyOn(subscription, 'unsubscribe');

      component.ngOnDestroy();

      expect(unsubscribeSpy).toHaveBeenCalled();
    });

    it('should not have memory leaks with multiple message subscriptions', () => {
      // Send multiple messages
      for (let i = 0; i < 10; i++) {
        dialogMessageService.sendMessage({ ...mockDialogMessage, title: `Message ${i}` });
      }

      // Component should only have the last message
      expect(component.message.title).toBe('Message 9');
    });

    it('should handle component destruction gracefully', () => {
      component.message = mockDialogMessage;
      component.customBody = {} as TemplateRef<any>;

      expect(() => {
        component.ngOnDestroy();
        fixture.destroy();
      }).not.toThrow();
    });
  });
});
